from flask import Flask, request, jsonify, render_template
import subprocess
import os
import datetime
import json
import tempfile
from time import sleep
import sys


app = Flask(__name__)


tool_path = ''
temp_json_path = ''
output_path = ''
mode = ''
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload():
    global output_path
    global tool_path
    global temp_json_path
    global mode

    current_time = datetime.datetime.now().strftime("%d-%m-%Y_%H%M%S") 
    input_dict = {}

    uploaded_file = request.files['file']
    tool_name = request.form['tool']
    mode = request.form['mode']
    
    output_path = f"AutoSpace_output_files\{tool_name}_{current_time}_output.txt"
    tool_path = f"python_tools/{tool_name}.py"
    
    uploaded_file_content = uploaded_file.read().decode('utf-8', errors='ignore')
    
    for id, row in enumerate(uploaded_file_content.split("\n"), 1):
        input_dict[id] = row

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_json:
        json.dump(input_dict, temp_json)
        temp_json_path = temp_json.name
    
    return jsonify({'message': 'success'})


@app.route('/runtool', methods=['POST'])
def runtool():
    global output_path
    global tool_path
    global temp_json_path
    global mode

    current_path = os.getcwd()
    output_path = os.path.join(current_path, output_path)
    total_output = 0

    status = 'running'
    
    while True:

        if os.stat(temp_json_path).st_size == 2:
            break
        
        if status == 'finished':
            break
        
        process = subprocess.Popen(
        ["python", "-u", tool_path, temp_json_path, output_path, mode])  # or use universal_newlines=True

        with open(temp_json_path, 'r') as f:
            input_dict = json.load(f)
            
        
        sleep(10)
        
        while True:
            old_file_size = os.stat(output_path).st_size

            sleep(60)
            if os.stat(temp_json_path).st_size == 2:
                status = 'finished'
                break

            if os.stat(output_path).st_size == old_file_size:
                    process.terminate()

                    with open(output_path, 'r', encoding='utf8') as f:
                        lines = f.readlines()

                    loop_finished_rows = len(lines) - total_output
                    residual_ids = dict(list(input_dict.items())[loop_finished_rows+1:])
                    
                    with open(temp_json_path, 'w') as f:
                        json.dump(residual_ids, f)
                    
                    total_output = len(lines)
                    print("input_file updated and process restarted")
                    break
        
    return jsonify({'file_path': output_path})

if __name__ == '__main__':
    app.run(host='0.0.0.0',port='5000', debug=True)