const toolsData = [
  {
    name: "Extractor",
    description: "Extracts text from the document line by line.",
    id: "generic"
  },
  {
    name: "Acrobat Si<PERSON>",
    description: "Compares two documents and underline the changes in the document while providing summary of the changes.",
    id: "compare"
  },
  {
    name: "Meta Data",
    description: `Extracts meta data from the document:
    - Document title
    - Document creation date
    - Document modification date
    - Document author`,
    id: "generic"
  },
  {
    name: "Keyword Search",
    description: `Searches for keywords in the document and returns the lines containing the keywords.`,
    id: "generic"
  },
  {
    name: "Part Search",
    description: `Searches for parts [single part or list of parts] in the document and returns 4 Status:
    - FOUND EXACT: Part found exactly as it's in the document.
    - FOUND DIFFERENT FORMAT: part found after remoing special characters like spaces, dots, etc.
    - NOT FOUND: Part not found in the document.
    - FOUND NON APLHA: Part found with additional characters.`,
    id: "generic"
  },
  {
    name: "Equal Manual",
    description: `> This tool is designed for the following purposes:
  1. Identify documents that are identical.
  2. Identify documents that are identical when dates are ignored.
  3. Notify users of potential reach or life cycle changes.
  4. Notify users of potentially unsearchable documents.
  5. Detect image changes based on the number of images in the document.
  6. Group documents based on:
     - Number of characters in the document.
     - Number of lines changed between the old and latest versions.`,
    id: "compare"
  },
  {
    name: "Magic",
    description: `> This tool is designed to:
  1. Extract all lines containing keywords specified in "keywords.xlsx".
  2. Compare extracted lines to highlight changes.
  3. Map keywords to their corresponding features using "mapping.xlsx".
> The input file ("input.xlsx") contains entries grouped by vendor code.
> Keywords and mapped features are vendor-specific to prevent conflicts.
> The output is generated in separate files, each named after the corresponding supplier.

** Note: Each supplier must have an associated keyword file and mapping database.`,
    id: "compare"
  },
  {
    name: "Image Compare",
    description: "Compares images between two documents and highlights changes by page and position.",
    id: "compare"
  },
  {
    name: "Catalogue Compare",
    description: "Compares two documents and identifies changed pages while ignoring date differences.",
    id: "compare"
  }
];


// Export the data for ES modules
export default toolsData;