/* Variables */
:root {
    /* Colors - Enhanced Glassmorphism Theme */
    --color-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    --color-bg-solid: #0a0a0a;
    --color-text: aliceblue;
    --color-text-secondary: rgba(240, 248, 255, 0.7);
    --color-border: rgba(255, 255, 255, 0.15);
    --color-glass: rgba(255, 255, 255, 0.05);
    --color-glass-hover: rgba(255, 255, 255, 0.1);
    --color-accent: #0f33ff;
    --color-accent-glow: rgba(15, 51, 255, 0.3);
    --color-card-bg: rgba(255, 255, 255, 0.03);
    --color-card-border: rgba(240, 248, 255, 0.3);

    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 18px;
    --spacing-xl: 24px;
    --spacing-xxl: 32px;

    /* Typography */
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 42px;

    /* Glassmorphism Effects */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: 1px solid rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glass-blur: blur(16px);
    --glass-hover-bg: rgba(255, 255, 255, 0.12);

    /* Layout */
    --border-standard: var(--glass-border);
    --box-padding: var(--spacing-lg);
    --letter-spacing-standard: 0.5px;
    --letter-spacing-wide: 5px;
    --section-gap: var(--spacing-md);
    --card-gap: var(--spacing-md);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
}

/* Base styles */
* {
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    padding: 0;
}

body {
    background: var(--color-bg);
    color: var(--color-text);
    line-height: 1.6;
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(15, 51, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(15, 51, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    width: 100%;
}

/* Common styles */
.section-container {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap);
    background: transparent;
}

.box {
    padding: var(--box-padding);
    background: var(--glass-bg);
    box-shadow: var(--glass-shadow);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    margin: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    opacity: 0.6;
}

.box:hover {
    background: var(--glass-hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}



.box-title {
    font-size: var(--font-size-md);
    font-weight: bolder;
    letter-spacing: var(--letter-spacing-standard);
    color: var(--color-text);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    padding-bottom: var(--spacing-xs);
    display: inline-block;
}

.secondary-text {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

/* Top Bar */
.top-bar {
    height: 80px;
    margin-bottom: var(--spacing-xxl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-bottom: var(--glass-border);
    letter-spacing: var(--letter-spacing-wide);
    position: relative;
    z-index: 100;
}

.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.logo {
    font-weight: bolder;
    font-size: var(--font-size-xl);
    letter-spacing: var(--letter-spacing-wide);
    background: linear-gradient(135deg, var(--color-accent), #1e4fff);
    height: 40px;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 0 20px var(--color-accent-glow);
    position: relative;
    overflow: hidden;
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px var(--color-accent-glow); }
    50% { box-shadow: 0 0 30px var(--color-accent-glow), 0 0 40px var(--color-accent-glow); }
}

/* Floating Animation for Cards */
.tool-card {
    animation: float 6s ease-in-out infinite;
}

.tool-card:nth-child(2) {
    animation-delay: -2s;
}

.tool-card:nth-child(3) {
    animation-delay: -4s;
}

.box {
    animation: float 8s ease-in-out infinite;
}

.box:nth-child(2) {
    animation-delay: -2.5s;
}

.logo {
    animation: glow 4s ease-in-out infinite;
}

/* Particle Effect Background */
.container::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(15, 51, 255, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(15, 51, 255, 0.1), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particles 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes particles {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.top-menu {
    display: flex;
    gap: var(--spacing-xxl);
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: var(--spacing-xxl);
    margin: 0;
    padding: 0;
}

nav ul li {
    position: relative;
}

nav ul li a {
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--font-size-md);
    letter-spacing: var(--letter-spacing-standard);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

nav ul li a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-accent);
    transition: width 0.3s ease;
}

nav ul li a:hover {
    color: var(--color-accent);
    background: var(--glass-hover-bg);
}

nav ul li a:hover::before {
    width: 100%;
}

.account {
    font-size: 30px;
    color: var(--color-text);
    transition: all 0.3s ease;
    cursor: pointer;
}

.account:hover {
    color: var(--color-accent);
    transform: scale(1.1);
}

/* Content Area */
.content {
    display: grid;
    grid-template-columns: 220px 420px 1.5fr 1fr;
    grid-template-rows: 1fr;
    min-height: calc(100vh - 200px);
    background: transparent;
    padding: var(--spacing-xl);
    gap: var(--spacing-lg);
    align-items: start;
}

/* Side Bar */
.side-bar {
    height: 100%;
    width: 100%;
    padding: var(--spacing-xl) var(--spacing-md);
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius-lg);
    position: relative;
    overflow: hidden;
}

.side-bar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
    width: 100%;
}

.tab {
    width: 100%;
    height: 60px;
    position: relative;
    border: 0;
    font-weight: normal;
    font-size: medium;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.tab:hover::before {
    left: 100%;
}

.tab.active {
    background: linear-gradient(135deg, var(--color-accent), #1e4fff);
    box-shadow: 0 0 20px var(--color-accent-glow);
    transform: scale(1.02);
}

.tab.active span {
    opacity: 1;
    transform: translate(-50%, -50%);
    background: transparent;
    color: white;
    font-weight: bolder;
}

.tab span {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(100%, -50%);
    font-weight: bolder;
    letter-spacing: 0.5px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

.tab:hover span {
    opacity: 1;
    transform: translate(-50%, -50%);
}

.tab .icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-text);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: var(--font-size-lg);
}

.tab:hover .icon {
    transform: translate(-50%, -50%) scale(0);
}

.tab.active .icon {
    transform: translate(-50%, -50%) scale(0);
}

/* Tool Cards Section */
.tools-section {
    transition: all 0.5s;
    display: flex;
    gap: var(--spacing-lg);
    flex-direction: column;
    padding: var(--spacing-xl);
    justify-content: flex-start;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius-lg);
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.tools-section::-webkit-scrollbar {
    width: 6px;
}

.tools-section::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.tools-section::-webkit-scrollbar-thumb {
    background: var(--color-accent);
    border-radius: 3px;
}

.tool-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid var(--color-card-border);
    width: 100%;
    padding: var(--spacing-lg);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    border-radius: var(--border-radius-md);
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.tool-card:hover {
    cursor: pointer;
    box-shadow: 0 8px 25px var(--color-accent-glow);
    transform: translateY(-4px) scale(1.02);
    border-color: var(--color-accent);
    background: var(--glass-hover-bg);
}

.tool-card span {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.tool-card h3 {
    margin: 0;
    font-size: var(--font-size-sm);
    font-weight: bold;
    color: white;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--color-accent), #1e4fff);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    box-shadow: 0 4px 15px var(--color-accent-glow);
    flex-shrink: 0;
}

.tool-card p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    margin: 0;
    line-height: 1.5;
}
.section h3{
    padding: 10px;
}

/* Middle Section */
.middle-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.section{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.description-box .tool-description{
    margin:0px;

}

.description-box{
    justify-content: space-between;
}

.tool-name {
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

.tool-description {
    margin-bottom: var(--spacing-lg);
    white-space: pre-line;
}

.button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}
.progress-section{
    flex-grow: 1;
}
.smaller-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.progress-box, .finished-box {
    flex-grow:  1;
    border-radius: var(--border-radius-sm);
}
.session-card{
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    border-left: 4px solid var(--color-accent);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-left: 4px solid var(--color-accent);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.session-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 4px;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--color-accent), transparent);
}

.session-card:hover {
    cursor: pointer;
    border-left-color: var(--color-text);
    transform: translateX(8px);
    background: var(--glass-hover-bg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.session-card:active{
    opacity: 0.8;
    transform: translateX(4px) scale(0.98);
}

.output-file{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.session-info, .click-copy{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
    align-items: center;
}

.session-indictor {
    filter: drop-shadow(0 0 4px currentColor);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.tool.name {
    font-weight: bolder;
    color: var(--color-text);
    background: var(--glass-bg);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-border);
}

/* Right Section */
.right-section {
    background-color: var(--color-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.visualization-box {
    flex-grow: 1;
}

.visualization-box ul {
    list-style-position: inside;
    padding-left: var(--spacing-md);
}

.visualization-box ul li {
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.visualization-box ul li::marker {
    color: red;
}

.users-box {
    height: 180px;
    border-radius: var(--border-radius-sm);
}

.user-count {
    color: var(--color-text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    font-style: italic;
}

/* Footer */
footer {
    padding: var(--spacing-xl);
    text-align: center;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-top: var(--glass-border);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.upload-button, .run-button, .download-sample-button {
    display: none;
    margin-top: var(--spacing-md);
    margin-right: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-md);
    cursor: pointer;
    color: var(--color-text);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius-md);
    outline: none;
    font-weight: bolder;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.upload-button::before, .run-button::before, .download-sample-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.upload-button:hover::before, .run-button:hover::before, .download-sample-button:hover::before {
    left: 100%;
}

.upload-button:hover, .run-button:hover, .download-sample-button:hover {
    background: linear-gradient(135deg, var(--color-accent), #1e4fff);
    color: white;
    border-color: var(--color-accent);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--color-accent-glow);
}

.upload-button:active, .run-button:active, .download-sample-button:active {
    transform: translateY(0) scale(0.98);
}

/* Additional Glassmorphism Effects */
.middle-section, .right-section {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--color-accent), #1e4fff);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1e4fff, var(--color-accent));
}

/* Enhanced Visual Effects */
.visualization-box ul li::marker {
    color: var(--color-accent);
}

.progress-box, .finished-box {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--border-radius-md);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content {
        grid-template-columns: 200px 350px 1fr 1fr;
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
        gap: var(--spacing-lg);
    }

    .side-bar {
        height: auto;
    }

    .tabs {
        flex-direction: row;
        gap: var(--spacing-md);
    }

    .tab {
        height: 50px;
        flex: 1;
    }
}